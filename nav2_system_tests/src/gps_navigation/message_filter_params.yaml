amcl:
  ros__parameters:
    # 增加消息过滤器队列大小
    transform_tolerance: 2.0  # 增加变换容忍度
    laser_max_beams: 30  # 减少激光束数量
    laser_model_type: "likelihood_field"  # 使用更高效的激光模型
    tf_message_filter_queue_size: 100  # 增加TF消息过滤器队列大小

local_costmap:
  local_costmap:
    ros__parameters:
      transform_tolerance: 2.0  # 增加变换容忍度
      voxel_layer:
        scan:
          observation_keep_time: 0.5  # 减少观察保持时间
          expected_update_rate: 0.5  # 降低期望更新频率
          tf_message_filter_queue_size: 100  # 增加TF消息过滤器队列大小

global_costmap:
  global_costmap:
    ros__parameters:
      transform_tolerance: 2.0  # 增加变换容忍度
      obstacle_layer:
        scan:
          observation_keep_time: 0.5  # 减少观察保持时间
          expected_update_rate: 0.5  # 降低期望更新频率
          tf_message_filter_queue_size: 100  # 增加TF消息过滤器队列大小
